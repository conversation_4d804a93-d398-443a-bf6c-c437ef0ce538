{"originHash": "ddda2a594d9fc369381a5858d78b426181c6e2bce1ba73837097fb4252b5fbd5", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "2781038865a80e2c425a1da12cc1327bcd56501f", "version": "1.7.6"}}, {"identity": "dkcamera", "kind": "remoteSourceControl", "location": "https://github.com/zhangao0086/DKCamera", "state": {"branch": "master", "revision": "5c691d11014b910aff69f960475d70e65d9dcc96"}}, {"identity": "dkimagepickercontroller", "kind": "remoteSourceControl", "location": "https://github.com/zhangao0086/DKImagePickerController", "state": {"branch": "4.3.9", "revision": "0bdfeacefa308545adde07bef86e349186335915"}}, {"identity": "dkphotogallery", "kind": "remoteSourceControl", "location": "https://github.com/zhangao0086/DKPhotoGallery", "state": {"branch": "master", "revision": "311c1bc7a94f1538f82773a79c84374b12a2ef3d"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "d1f7c7e8eaa74d7e44467184dc5f592268247d33", "version": "11.11.0"}}, {"identity": "flutterfire", "kind": "remoteSourceControl", "location": "https://github.com/firebase/flutterfire", "state": {"revision": "a80a123386fd4904cad6938673020a7bcf31b2f2", "version": "3.13.0-firebase-core-swift"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "dd89fc79a77183830742a16866d87e4e54785734", "version": "11.11.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS.git", "state": {"revision": "65fb3f1aa6ffbfdc79c4e22178a55cd91561f5e9", "version": "8.0.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "53156c7ec267db846e6b64c9f4c4e31ba4cf75eb", "version": "8.0.2"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage", "state": {"revision": "cac9a55a3ae92478a2c95042dcc8d9695d2129ca", "version": "5.21.0"}}, {"identity": "sentry-cocoa", "kind": "remoteSourceControl", "location": "https://github.com/getsentry/sentry-cocoa", "state": {"revision": "4589f82132097558d386710b139bfe9c35c64d4a", "version": "8.48.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "ebc7251dd5b37f627c93698e4374084d98409633", "version": "1.28.2"}}, {"identity": "<PERSON><PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/kirualex/SwiftyGif.git", "state": {"revision": "4430cbc148baa3907651d40562d96325426f409a", "version": "5.4.5"}}, {"identity": "tocropviewcontroller", "kind": "remoteSourceControl", "location": "https://github.com/TimOliver/TOCropViewController.git", "state": {"revision": "a634cb7cdfd580006e79a6e74e64417fe9e9783b", "version": "2.7.4"}}], "version": 3}